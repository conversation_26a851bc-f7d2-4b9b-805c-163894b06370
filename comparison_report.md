# 机器人训练项目对比分析报告 (`leisaac`, `lerobot`, `openpi`)

本文档旨在深入比较 `leisaac`、`lerobot` 和 `openpi` 三个项目在训练机械臂时所使用的不同模型和训练方法。

## 1. 项目定位与关系

这三个项目在机器人学习生态中扮演着不同但相互关联的角色。它们的关系可以用一个层次化的图来表示：

```mermaid
graph TD
    subgraph "应用层 (Application Layer)"
        A[leisaac]
    end
    subgraph "框架层 (Framework Layer)"
        B[lerobot]
        C[openpi]
    end
    subgraph "基础库/模拟器 (Libraries / Simulators)"
        D[Isaac Sim]
        E[PyTorch]
        F[JAX/Flax]
        G[Hugging Face]
    end

    A -- "使用/Depends on" --> B
    A -- "使用/Depends on" --> D
    B -- "构建于/Built on" --> E
    B -- "集成/Integrates with" --> G
    C -- "构建于/Built on" --> F
    C -- "使用/Uses" --> B
```

*   **`lerobot`**: 是一个功能全面的**机器人学习框架**。它提供了从数据处理、模型定义到完整训练循环（包括模仿学习和强化学习）的所有核心组件。它基于 PyTorch，并与 Hugging Face 生态系统紧密集成，旨在成为一个通用的、易于扩展的机器人技术研发平台。
*   **`openpi`**: 是一个专注于**微调大型预训练机器人基础模型**的框架。它基于 JAX/Flax，专为高性能、可扩展的训练而设计。它的核心思想不是从零开始训练，而是利用强大的基础模型（如 Google 的 Gemma），通过 LoRA 等技术在特定任务上进行高效微调。它也会使用 `lerobot` 的数据集格式。
*   **`leisaac`**: 是一个**应用层项目**，它本身不包含核心的训练算法或模型。它的定位是作为 `lerobot` 和 NVIDIA `Isaac Sim` 模拟器之间的桥梁。其主要功能是在 `Isaac Sim` 中通过 `lerobot` 的硬件（如 SO101 Leader）进行遥操作来收集数据，并将这些数据用于微调外部的大型模型（如 NVIDIA 的 GR00T）。

## 2. 核心模型对比

| 项目 | 核心模型/架构 | 特点 |
| :--- | :--- | :--- |
| **`leisaac`** | **GR00T N1.5** (外部模型) | 不包含内建模型。其设计目的是调用一个外部的、服务化的策略模型进行推理和评估。 |
| **`lerobot`** | **多种内建模型库** | 提供了一个丰富的策略库，包括：<br>- **模仿学习**: `ACT` (Action Chunking w/ Transformers), `Diffusion Policy`, `VQ-BeT`<br>- **强化学习**: `SAC` (Soft Actor-Critic), `TD-MPC`<br>- **视觉语言模型**: `pi0`, `pi0fast`, `smolvla` |
| **`openpi`** | **`pi0` / `pi0-fast`** | 核心是 `pi0` 模型，这是一个基于 Google `Gemma` 的大型视觉语言模型。支持通过 `LoRA` 进行高效微调。其模型库还包括 `ViT`, `SigLIP` 等基础视觉模型。 |

## 3. 训练方法对比

| 项目 | 训练方法 | 核心脚本/逻辑 |
| :--- | :--- | :--- |
| **`leisaac`** | **无本地训练** | 项目本身不执行训练。它只负责**数据收集** (`teleop_se3_agent.py`) 和**数据转换** (`isaaclab2lerobot.py`)，然后将数据交给外部的训练流程（如 GR00T 的微调脚本）。 |
| **`lerobot`** | **从零训练 / 微调** | - **模仿学习**: 使用统一的 `scripts/train.py` 脚本，通过配置文件选择不同的策略进行离线训练。<br>- **强化学习 (SAC)**: 采用**分布式 Actor-Learner 架构**。`scripts/rl/learner.py` 负责模型更新，`scripts/rl/actor.py` 负责与环境交互和数据收集。 |
| **`openpi`** | **微调 (Fine-tuning)** | 核心是**微调**。`training/config.py` 中定义了一系列预设的 `TrainConfig`，每个配置都指定了要加载的**基础模型权重** (`weight_loader`) 和要微调的数据集。训练流程由一个未在代码库中提供的主脚本（通常在 JAX 项目中称为 `main.py` 或 `train.py`）驱动，该脚本会加载这些配置。 |

### `lerobot` 中 SAC 与环境的交互方式

`lerobot` 的 SAC 训练流程清晰地展示了其与环境的交互模式。这个过程完全由 [`lerobot/src/lerobot/scripts/rl/actor.py`](lerobot/src/lerobot/scripts/rl/actor.py) 驱动，其核心是一个经典的强化学习交互循环。

1.  **环境初始化**: 在 `actor.py` 中，通过 `make_robot_env` 创建一个 `gymnasium` 兼容的环境。
2.  **交互循环**:
    a.  **动作选择**: `policy.select_action(obs)` 从当前观测值生成动作。
    b.  **环境步骤**: `env.step(action)` 将动作应用于环境，获得 `(next_obs, reward, done, ...)`。
    c.  **经验存储**: 将交互元组 `(obs, action, reward, next_obs, done)` 存储起来。
3.  **数据传输**: 当一个 Episode 结束后，收集到的所有经验元组被打包并通过 gRPC 发送给 `learner.py`。
4.  **模型更新**: `actor.py` 从 `learner.py` 接收最新的模型参数，并更新本地策略。

## 4. 技术栈对比

| 项目 | 主要框架 | 核心依赖 |
| :--- | :--- | :--- |
| **`leisaac`** | - | `Isaac Sim`, `lerobot` |
| **`lerobot`** | **PyTorch** | `gymnasium`, `huggingface_hub`, `grpcio` |
| **`openpi`** | **JAX / Flax** | `tyro`, `etils`, `lerobot` (用于数据集) |

## 5. 总结

*   **`leisaac`** 是一个**数据收集工具**，专注于连接 `Isaac Sim` 和 `lerobot` 生态，为大型模型的微调提供高质量的仿真数据。
*   **`lerobot`** 是一个**通用的、功能齐全的机器人学习框架**，基于 PyTorch，支持多种模仿学习和强化学习算法的从零开始的训练。它的设计使其成为一个理想的学术研究和快速原型验证平台。
*   **`openpi`** 是一个**专注于微调大型基础模型的工业级框架**，基于 JAX/Flax，追求极致的训练性能和可扩展性。它的目标是利用最先进的视觉语言模型来解决复杂的机器人任务。

这三个项目共同构成了一个从数据收集、通用算法研究到大规模模型微调的完整机器人学习工作流。

---

## 附录：SAC 与环境交互的深度解析

`lerobot` 中 SAC 策略与环境的交互是一个分层协作的过程，其核心是 `gymnasium` 的标准 `step` 接口。

### 1. 交互的起点: `actor.py`

交互的起点是 [`lerobot/src/lerobot/scripts/rl/actor.py`](lerobot/src/lerobot/scripts/rl/actor.py) 中的 `act_with_policy` 函数。它负责执行经典的强化学习循环：

```python
# 来自 lerobot/src/lerobot/scripts/rl/actor.py

def act_with_policy(...):
    # 1. 初始化环境和策略
    online_env = make_robot_env(cfg=cfg.env)
    policy: SACPolicy = make_policy(...)
    obs, info = online_env.reset()

    # 2. 开始交互循环
    for interaction_step in range(cfg.policy.online_steps):
        # 2a. 策略根据当前观测值选择动作
        action = policy.select_action(batch=obs)

        # 2b. 将动作应用于环境，获得反馈
        next_obs, reward, done, truncated, info = online_env.step(action)

        # ... (省略存储和发送 transition 的逻辑) ...
```

这里的 `online_env.step(action)` 是一个标准的 `gymnasium` 调用，但它的背后实现却是由多个类协作完成的。

### 2. `step(action)` 的实现: `gym_manipulator.py`

`online_env` 对象是由 [`lerobot/src/lerobot/scripts/rl/gym_manipulator.py`](lerobot/src/lerobot/scripts/rl/gym_manipulator.py) 中的 `make_robot_env` 函数创建的。这个函数使用**装饰器模式**，将一系列功能（Wrappers）包裹在一个基础的 `RobotEnv` 环境之上。

`step(action)` 的调用流程如下：

```mermaid
sequenceDiagram
    participant A as actor.py
    participant W1 as TorchActionWrapper
    participant W2 as GamepadControlWrapper
    participant R as RobotEnv (Base)
    participant H as Robot Hardware
    
    A->>W1: env.step(torch_action)
    W1->>W2: step(numpy_action)
    Note right of W1: 将 torch.Tensor 转为 np.ndarray
    
    W2->>R: step(final_action)
    Note right of W2: 检查人类干预<br/>可能会用人类动作替换 `final_action`
    
    R->>H: self.robot.send_action(action_dict)
    Note right of R: 将动作发送给物理机器人
    
    H-->>R: 返回新的传感器读数
    R->>W2: 返回 (next_obs, reward, done, ...)
    
    W2-->>W1: 返回 (next_obs, reward, done, ...)
    Note left of W2: Wrapper 可能会修改返回的 reward 或 info
    
    W1-->>A: 返回最终的 (next_obs, reward, done, ...)
```

**关键实现点**:

- **`make_robot_env` ([`gym_manipulator.py:1840`](lerobot/src/lerobot/scripts/rl/gym_manipulator.py:1840))**: 这是环境的“工厂”函数，它根据配置组装 `RobotEnv` 和各种 Wrappers。
- **`RobotEnv.step` ([`gym_manipulator.py:350`](lerobot/src/lerobot/scripts/rl/gym_manipulator.py:350))**: 这是与物理世界交互的入口。它的核心是调用 **`self.robot.send_action(action_dict)`**。
- **`self.robot.send_action`**: 这个调用的背后是一个从**末端执行器空间**到**关节空间**再到**电机指令**的完整转换链：
  1.  **末端执行器空间控制** ([`so100_follower_end_effector.py:95`](lerobot/src/lerobot/robots/so100_follower/so100_follower_end_effector.py:95)): `SO100FollowerEndEffector` 类接收 `action_dict`（包含 `delta_x`, `delta_y`, `delta_z`）。它通过**逆运动学 (Inverse Kinematics)** 计算出每个关节需要达到的目标角度。
  2.  **关节空间控制** ([`so100_follower.py:190`](lerobot/src/lerobot/robots/so100_follower/so100_follower.py:190)): `SO100Follower` 类接收计算出的目标关节角度。
  3.  **电机指令发送**: `SO100Follower` 类通过 `self.bus.sync_write("Goal_Position", ...)` 将这些角度作为“目标位置”指令，通过电机总线（一个封装了串口通信的底层接口）发送给物理机器人。
- **Wrappers**:
  - **`GamepadControlWrapper.step` ([`gym_manipulator.py:1629`](lerobot/src/lerobot/scripts/rl/gym_manipulator.py:1629))**: 在调用内部环境的 `step` 之前，它会检查手柄输入。如果检测到人类干预，它会**忽略**策略传入的 `action`，转而使用人类的输入。
  - **`TorchActionWrapper.step` ([`gym_manipulator.py:198`](lerobot/src/lerobot/scripts/rl/gym_manipulator.py:198))**: 它是最外层的 Wrapper 之一，负责将 SAC 策略输出的 `torch.Tensor` 转换为 `RobotEnv` 所期望的 `np.ndarray`。

通过这种分层和解耦的设计，`lerobot` 成功地将高层的强化学习算法与底层的硬件控制和复杂的人机交互逻辑清晰地分离开来。
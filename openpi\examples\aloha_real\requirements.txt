# This file was autogenerated by uv via the following command:
#    uv pip compile examples/aloha_real/requirements.in -o examples/aloha_real/requirements.txt --python-version 3.10
absl-py==2.1.0
    # via
    #   dm-control
    #   dm-env
    #   labmaze
    #   mujoco
catkin-pkg==1.0.0
    # via rospkg
certifi==2024.8.30
    # via requests
charset-normalizer==3.4.0
    # via requests
contourpy==1.1.1
    # via matplotlib
cycler==0.12.1
    # via matplotlib
distro==1.9.0
    # via rospkg
dm-control==1.0.23
    # via -r examples/aloha_real/requirements.in
dm-env==1.6
    # via dm-control
dm-tree==0.1.8
    # via
    #   dm-control
    #   dm-env
docstring-parser==0.16
    # via tyro
docutils==0.20.1
    # via catkin-pkg
einops==0.8.0
    # via -r examples/aloha_real/requirements.in
etils==1.3.0
    # via mujoco
fonttools==4.55.2
    # via matplotlib
glfw==2.8.0
    # via
    #   dm-control
    #   mujoco
h5py==3.11.0
    # via -r examples/aloha_real/requirements.in
idna==3.10
    # via requests
importlib-resources==6.4.5
    # via etils
kiwisolver==1.4.7
    # via matplotlib
labmaze==1.0.6
    # via dm-control
lxml==5.3.0
    # via dm-control
markdown-it-py==3.0.0
    # via rich
matplotlib==3.7.5
    # via -r examples/aloha_real/requirements.in
mdurl==0.1.2
    # via markdown-it-py
modern-robotics==1.1.1
    # via -r examples/aloha_real/requirements.in
msgpack==1.1.0
    # via -r examples/aloha_real/requirements.in
mujoco==3.2.3
    # via dm-control
numpy==1.24.4
    # via
    #   -r examples/aloha_real/requirements.in
    #   contourpy
    #   dm-control
    #   dm-env
    #   h5py
    #   labmaze
    #   matplotlib
    #   modern-robotics
    #   mujoco
    #   opencv-python
    #   pyquaternion
    #   scipy
opencv-python==*********
    # via -r examples/aloha_real/requirements.in
packaging==24.2
    # via
    #   -r examples/aloha_real/requirements.in
    #   matplotlib
pexpect==4.9.0
    # via -r examples/aloha_real/requirements.in
pillow==10.4.0
    # via
    #   -r examples/aloha_real/requirements.in
    #   matplotlib
protobuf==5.29.1
    # via dm-control
ptyprocess==0.7.0
    # via pexpect
pygments==2.18.0
    # via rich
pyopengl==3.1.7
    # via
    #   dm-control
    #   mujoco
pyparsing==3.1.4
    # via
    #   catkin-pkg
    #   dm-control
    #   matplotlib
pyquaternion==0.9.9
    # via -r examples/aloha_real/requirements.in
pyrealsense2==2.55.1.6486
    # via -r examples/aloha_real/requirements.in
python-dateutil==2.9.0.post0
    # via
    #   catkin-pkg
    #   matplotlib
pyyaml==6.0.2
    # via
    #   -r examples/aloha_real/requirements.in
    #   rospkg
requests==2.32.3
    # via
    #   -r examples/aloha_real/requirements.in
    #   dm-control
rich==13.9.4
    # via tyro
rospkg==1.5.1
    # via -r examples/aloha_real/requirements.in
scipy==1.10.1
    # via dm-control
setuptools==75.3.0
    # via
    #   catkin-pkg
    #   dm-control
    #   labmaze
shtab==1.7.1
    # via tyro
six==1.17.0
    # via python-dateutil
tqdm==4.67.1
    # via dm-control
typeguard==4.4.0
    # via tyro
typing-extensions==4.12.2
    # via
    #   etils
    #   rich
    #   typeguard
    #   tyro
tyro==0.9.2
    # via -r examples/aloha_real/requirements.in
urllib3==2.2.3
    # via requests
websockets==14.1
    # via -r examples/aloha_real/requirements.in
zipp==3.20.2
    # via etils

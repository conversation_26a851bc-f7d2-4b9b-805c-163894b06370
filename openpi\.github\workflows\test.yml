name: Test
on:
  pull_request:
    branches:
      - "*"

jobs:
  run_tests:
    name: Run Tests
    runs-on: openpi-verylarge
    env:
      GIT_LFS_SKIP_SMUDGE: true
    steps:
      - uses: actions/checkout@v4

      - name: Install FFmpeg dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y ffmpeg libavcodec-dev libavformat-dev libavutil-dev

      - name: Install uv
        uses: astral-sh/setup-uv@v5

      - name: Set up Python
        run: uv python install

      - name: Install the project
        run: uv sync --all-extras --dev

      - name: Run tests
        run: uv run pytest --strict-markers -m "not manual"

# C++
**/cmake-build*/
**/build*/
**/*.so
**/*.log*

# Omniverse
**/*.dmp
**/.thumbs

# Python
.DS_Store
**/*.egg-info/
**/__pycache__/
**/.pytest_cache/
**/*.pyc
**/*.pb

# Docker/Singularity
**/*.sif
docker/cluster/exports/
docker/.container.cfg

# IDE
**/.idea/
**/.vscode/
# Don't ignore the top-level .vscode directory as it is
# used to configure VS Code settings
!.vscode

# Outputs
**/output/*
**/outputs/*
**/videos/*
**/wandb/*
**/.neptune/*
docker/artifacts/
*.tmp

# Doc Outputs
**/docs/_build/*
**/generated/*

# Isaac-Sim packman
_isaac_sim*
_repo
_build
.lastformat

# RL-Games
**/runs/*
**/logs/*
**/recordings/*

# Pre-Trained Checkpoints
/.pretrained_checkpoints/

# Teleop Recorded Dataset
/datasets/

# Tests
tests/

# Docker history
.isaac-lab-docker-history

# lerobot cache
source/leisaac/leisaac/devices/lerobot/.cache/so101_leader.json
source/leisaac/leisaac/devices/lerobot/.cache/left_so101_leader.json
source/leisaac/leisaac/devices/lerobot/.cache/right_so101_leader.json

# scene assets
assets/scenes/*
!assets/scenes/.gitkeep
assets/robots/*
!assets/robots/.gitkeep
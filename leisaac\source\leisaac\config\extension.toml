[package]

# Semantic Versioning is used: https://semver.org/
version = "0.1.0"

# Description
category = "isaaclab"
readme  = "README.md"

title = "Extension Template"
author = "Isaac Lab Project Developers"
maintainer = "Isaac Lab Project Developers"
description="Extension Template for Isaac Lab"
repository = "https://github.com/isaac-sim/IsaacLab.git"
keywords = ["extension", "template", "isaaclab"]

[dependencies]
"isaaclab" = {}
"isaaclab_assets" = {}
"isaaclab_mimic" = {}
"isaaclab_rl" = {}
"isaaclab_tasks" = {}
# NOTE: Add additional dependencies here

[[python.module]]
name = "leisaac"

[isaaclab_settings]
# TODO: Uncomment and list any apt dependencies here.
#       If none, leave it commented out.
# apt_deps = ["example_package"]
# TODO: Uncomment and provide path to a ros_ws
#       with rosdeps to be installed. If none,
#       leave it commented out.
# ros_ws = "path/from/extension_root/to/ros_ws"
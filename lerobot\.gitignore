# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

### Environments & Dependencies ###
.env
.venv
env/
venv/
env.bak/
venv.bak/
.python-version
__pypackages__/
node_modules/

# Lock files
poetry.lock
uv.lock
Pipfile.lock

### Build & Distribution ###
build/
dist/
sdist/
wheels/
downloads/
eggs/
.eggs/
parts/
var/
pip-wheel-metadata/
share/python-wheels/
develop-eggs/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
lib/
lib64/

# PyInstaller
*.manifest
*.spec

### Compiled & Cached Files ###
__pycache__/
*.py[cod]
*$py.class
*.so
*.sage.py
.cache/
.ruff_cache/
.mypy_cache/
.pyre/
.pytype/
cython_debug/

### Testing & Coverage ###
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.pytest_cache/
.hypothesis/
nosetests.xml
coverage.xml
*.cover
*.py,cover
!tests/artifacts

### Logs & Temporary Files ###
logs/
tmp/
*.log
pip-log.txt
pip-delete-this-directory.txt
celerybeat-schedule
celerybeat.pid

### IDE & Editor Config ###
# VS Code
.vscode/
.devcontainer/

# JetBrains / PyCharm
.idea/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# Vim
*.swp

# Other
*~

### OS Specific ###
# macOS
.DS_Store

# Windows
Thumbs.db

### Framework & Tool Specific ###

.Python

# Django
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Jupyter
.ipynb_checkpoints/
profile_default/
ipython_config.py

# Sphinx
docs/_build/

# MkDocs
/site

# PyBuilder
.pybuilder/
target/

# mypy
.dmypy.json
dmypy.json

### HPC & Slurm ###
nautilus/*.yaml
*.key
sbatch*.sh

### Miscellaneous ###
# W&B
wandb/

# Dev scripts
.dev/

# Data folders
data/
outputs/

# Translations
*.mo
*.pot

# Dev folders
.cache/*
